import asyncio
from app.db.session import get_db
from app.models.user import User, UserRole
from sqlalchemy import select
from sqlalchemy.orm import selectinload

async def check_user_and_roles():
    async for db in get_db():
        # 检查用户信息
        result = await db.execute(
            select(User)
            .options(selectinload(User.role).selectinload(UserRole.permissions))
            .where(User.username == '17604840253')
        )
        user = result.scalar_one_or_none()
        
        if user:
            print(f'用户存在: {user.username}')
            print(f'用户ID: {user.id}')
            print(f'角色ID: {user.role_id}')
            print(f'角色: {user.role.name if user.role else "无角色"}')
            print(f'权限数量: {len(user.role.permissions) if user.role else 0}')
            
            if user.role and user.role.permissions:
                print('权限列表:')
                for p in user.role.permissions:
                    print(f'  - {p.code}: {p.name}')
        else:
            print('用户不存在')
            
        # 检查所有角色的权限情况
        print('\n=== 所有角色权限情况 ===')
        roles_result = await db.execute(
            select(UserRole)
            .options(selectinload(UserRole.permissions))
        )
        roles = roles_result.scalars().all()
        
        for role in roles:
            print(f'角色: {role.name} (ID: {role.id})')
            print(f'  权限数量: {len(role.permissions)}')
            if role.permissions:
                for p in role.permissions[:5]:  # 只显示前5个权限
                    print(f'    - {p.code}: {p.name}')
                if len(role.permissions) > 5:
                    print(f'    ... 还有 {len(role.permissions) - 5} 个权限')
            print()
        
        break

if __name__ == '__main__':
    asyncio.run(check_user_and_roles())