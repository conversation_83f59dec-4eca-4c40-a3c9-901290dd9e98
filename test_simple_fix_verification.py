#!/usr/bin/env python3
"""
简单验证权限修复效果
直接测试关键的权限检查逻辑
"""

import asyncio
from typing import Optional

# 模拟核心组件
class MockArticle:
    def __init__(self, id: int, is_published: bool = True, is_approved: bool = True, author_id: int = 1):
        self.id = id
        self.is_published = is_published
        self.is_approved = is_approved
        self.author_id = author_id

class MockUser:
    def __init__(self, id: int, username: str, is_superuser: bool = False, role_id: Optional[int] = None):
        self.id = id
        self.username = username
        self.is_superuser = is_superuser
        self.role_id = role_id

class MockPermission:
    def __init__(self, resource_type, action, scope):
        self.resource_type = resource_type
        self.action = action
        self.scope = scope

class MockResourceType:
    ARTICLE = "ARTICLE"

class MockAction:
    READ = type('Action', (), {'value': 'READ'})()

class MockScope:
    PUBLIC = type('Scope', (), {'value': 'PUBLIC'})()
    OWN = type('Scope', (), {'value': 'OWN'})()
    ALL = type('Scope', (), {'value': 'ALL'})()

class MockPermissionChecker:
    @staticmethod
    async def check_permission(db, user, permission, resource=None):
        """
        模拟修复后的权限检查逻辑
        关键修复：现在接收resource参数并进行公开资源检查
        """
        # 超级管理员拥有所有权限
        if user and getattr(user, 'is_superuser', False):
            return True

        # 优先检查公开资源权限（对所有用户包括未登录用户）
        if permission.scope.value == "PUBLIC" and resource:
            is_public = getattr(resource, "is_published", False) and getattr(resource, "is_approved", False)
            if is_public and permission.action.value == "READ":
                print(f"  ✅ 公开资源权限检查通过: 文章{resource.id}是公开的")
                return True
            else:
                print(f"  ❌ 公开资源权限检查失败: 文章{resource.id}不是公开的")

        # 模拟用户权限检查（简化版）
        if user:
            print(f"  ℹ️  用户{user.id}的权限检查（简化模拟）")
            # 这里可以添加更复杂的用户权限逻辑
            return False
        else:
            print(f"  ℹ️  未登录用户，只能通过公开资源权限")
            return False

class MockArticlePermissionService:
    @staticmethod
    async def check_article_access(db, content, current_user=None):
        """
        修复后的文章访问权限检查
        关键修复：传入content作为resource参数
        """
        print(f"\n检查文章{content.id}的访问权限:")
        print(f"  文章状态: 发布={content.is_published}, 审核={content.is_approved}")
        print(f"  用户: {current_user.username if current_user else '未登录'}")
        
        if not content:
            return False

        # 检查对所有文章的读取权限
        all_permission = MockPermission(MockResourceType.ARTICLE, MockAction.READ, MockScope.ALL)
        if await MockPermissionChecker.check_permission(db, current_user, all_permission):
            print(f"  ✅ 通过ALL权限访问")
            return True

        # 检查对自己文章的读取权限
        if current_user and content.author_id == current_user.id:
            own_permission = MockPermission(MockResourceType.ARTICLE, MockAction.READ, MockScope.OWN)
            if await MockPermissionChecker.check_permission(db, current_user, own_permission):
                print(f"  ✅ 通过OWN权限访问")
                return True

        # 检查对公开文章的读取权限（修复：传入content作为resource参数）
        if content.is_published and content.is_approved:
            public_permission = MockPermission(MockResourceType.ARTICLE, MockAction.READ, MockScope.PUBLIC)
            if await MockPermissionChecker.check_permission(db, current_user, public_permission, content):
                print(f"  ✅ 通过PUBLIC权限访问")
                return True

        print(f"  ❌ 无权限访问")
        return False

    @staticmethod
    async def filter_accessible_articles(db, user, article_ids):
        """过滤用户可访问的文章ID列表"""
        if not article_ids:
            return []
        
        print(f"\n过滤可访问文章，输入ID: {article_ids}")
        
        # 模拟从数据库获取文章
        all_articles = {
            1: MockArticle(1, True, True, 1),   # 公开
            3: MockArticle(3, True, True, 2),   # 公开
            4: MockArticle(4, True, True, 1),   # 公开
            7: MockArticle(7, True, True, 3),   # 公开
            9: MockArticle(9, False, False, 1), # 私有
            14: MockArticle(14, True, True, 2), # 公开
            15: MockArticle(15, True, True, 1), # 公开
            19: MockArticle(19, True, True, 3), # 公开
            21: MockArticle(21, True, True, 2), # 公开
            22: MockArticle(22, True, True, 1), # 公开
        }
        
        requested_articles = [all_articles[id] for id in article_ids if id in all_articles]
        
        accessible_ids = []
        for article in requested_articles:
            # 检查每篇文章的访问权限
            if await MockArticlePermissionService.check_article_access(db, article, user):
                accessible_ids.append(article.id)
                
        return accessible_ids

async def test_fix_verification():
    """验证修复效果"""
    print("=== 权限修复验证测试 ===")
    
    # 测试数据 - 与用户报告的相同
    article_ids = [4, 9, 7, 15, 19, 21, 14, 3, 22, 1]
    
    print(f"\n测试场景: 未登录用户批量获取文章")
    print(f"请求的文章ID: {article_ids}")
    
    # 测试未登录用户访问
    accessible_articles = await MockArticlePermissionService.filter_accessible_articles(
        db=None, user=None, article_ids=article_ids
    )
    
    print(f"\n结果: 可访问的文章ID: {accessible_articles}")
    
    # 验证结果
    expected_public_ids = [1, 3, 4, 7, 14, 15, 19, 21, 22]  # 排除私有文章9
    
    if accessible_articles:
        print(f"\n✅ 修复成功！未登录用户可以访问 {len(accessible_articles)} 篇公开文章")
        
        if set(accessible_articles) == set(expected_public_ids):
            print("✅ 返回的文章ID完全正确")
        else:
            print(f"⚠️  返回的文章ID部分正确")
            print(f"   期望: {sorted(expected_public_ids)}")
            print(f"   实际: {sorted(accessible_articles)}")
            missing = set(expected_public_ids) - set(accessible_articles)
            extra = set(accessible_articles) - set(expected_public_ids)
            if missing:
                print(f"   缺失: {sorted(missing)}")
            if extra:
                print(f"   多余: {sorted(extra)}")
    else:
        print("\n❌ 修复失败！未登录用户仍然无法访问公开文章")
    
    return len(accessible_articles) > 0

async def test_single_article_access():
    """测试单篇文章访问"""
    print("\n=== 单篇文章访问测试 ===")
    
    # 测试公开文章
    public_article = MockArticle(1, True, True, 1)
    has_access = await MockArticlePermissionService.check_article_access(
        db=None, content=public_article, current_user=None
    )
    
    print(f"\n未登录用户访问公开文章结果: {has_access}")
    
    # 测试私有文章
    private_article = MockArticle(9, False, False, 1)
    has_access_private = await MockArticlePermissionService.check_article_access(
        db=None, content=private_article, current_user=None
    )
    
    print(f"未登录用户访问私有文章结果: {has_access_private}")
    
    return has_access and not has_access_private

async def main():
    """主函数"""
    print("批量获取文章权限修复验证")
    print("=" * 50)
    
    # 测试1: 批量文章过滤
    batch_success = await test_fix_verification()
    
    # 测试2: 单篇文章访问
    single_success = await test_single_article_access()
    
    print("\n" + "=" * 50)
    print("修复验证总结:")
    print(f"批量文章过滤: {'✅ 通过' if batch_success else '❌ 失败'}")
    print(f"单篇文章访问: {'✅ 通过' if single_success else '❌ 失败'}")
    
    if batch_success and single_success:
        print("\n🎉 所有测试通过！权限修复成功！")
        print("\n修复关键点:")
        print("1. 在ArticlePermissionService.check_article_access方法中")
        print("2. 为PUBLIC权限检查传入content作为resource参数")
        print("3. 使PermissionChecker能够正确识别公开资源")
        print("4. 未登录用户现在可以访问公开文章")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())