import asyncio
from app.db.session import get_db
from app.db.init_permissions import init_permissions
from app.models.user import Permission, UserRole
from sqlalchemy import select, delete

async def reinit_permissions():
    async for db in get_db():
        print('开始重新初始化权限...')
        
        # 清除现有的角色权限关联
        print('清除现有角色权限关联...')
        from app.models.user import role_permission
        await db.execute(delete(role_permission))
        await db.commit()
        
        # 清除现有权限
        print('清除现有权限...')
        await db.execute(delete(Permission))
        await db.commit()
        
        # 重新初始化权限
        print('重新初始化权限...')
        await init_permissions(db)
        
        print('权限重新初始化完成！')
        break

if __name__ == '__main__':
    asyncio.run(reinit_permissions())