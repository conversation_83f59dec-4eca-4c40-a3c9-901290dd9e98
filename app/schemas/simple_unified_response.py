"""
简化的统一响应模型 - 兼容现有middleware

这个版本与现有的ResponseFormatterMiddleware兼容，
middleware会自动将响应包装为 {"status": "success", "data": <返回值>}
"""

from datetime import datetime
from typing import Any, TypeVar

from pydantic import BaseModel, ConfigDict, Field

# 泛型类型变量
T = TypeVar("T")


class ContentStats(BaseModel):
    """内容统计信息"""

    like_count: int = Field(0, description="点赞数")
    favorite_count: int = Field(0, description="收藏数")
    visit_count: int = Field(0, description="访问次数")
    comment_count: int = Field(0, description="评论数")
    share_count: int = Field(0, description="分享数")

    # 用户相关统计
    is_liked_by_user: bool = Field(False, description="当前用户是否已点赞")
    is_favorited_by_user: bool = Field(False, description="当前用户是否已收藏")
    is_followed_author: bool = Field(False, description="当前用户是否已关注作者")


class ContentMeta(BaseModel):
    """内容元数据"""

    content_type: str = Field(..., description="内容类型: article, video")
    content_id: int = Field(..., description="内容ID")
    slug: str | None = Field(None, description="内容别名")
    seo_title: str | None = Field(None, description="SEO标题")
    seo_description: str | None = Field(None, description="SEO描述")
    keywords: list[str] | None = Field(None, description="关键词")


class UserInfo(BaseModel):
    """用户信息简化模型"""

    id: int
    username: str
    nickname: str | None = None
    avatar: str | None = None

    model_config = ConfigDict(from_attributes=True)


class CategoryInfo(BaseModel):
    """分类信息简化模型"""

    id: int
    name: str
    slug: str | None = None

    model_config = ConfigDict(from_attributes=True)


class ReviewInfo(BaseModel):
    """审核信息简化模型"""

    id: int
    status: str
    comment: str | None = None
    reviewer_id: int | None = None
    reviewed_at: datetime | None = None

    model_config = ConfigDict(from_attributes=True)


class UnifiedContentResponse(BaseModel):
    """统一内容响应模型

    这个模型可以用于文章、视频等所有内容类型的响应，
    通过可选字段来控制返回的数据内容。

    由于使用了全局ResponseFormatterMiddleware，
    这个模型会被自动包装为 {"status": "success", "data": <this_model>}
    """

    # 基础字段
    id: int
    title: str
    description: str | None = None
    content: str | None = Field(None, description="内容正文，列表接口通常不返回")
    cover_url: str | None = None

    # 状态字段
    is_published: bool = False
    is_approved: bool = False

    # 时间字段
    created_at: datetime
    updated_at: datetime
    published_at: datetime | None = None

    # 关联字段
    author: UserInfo
    category: CategoryInfo | None = None
    tags: list[str] | None = None

    # 可选扩展字段
    stats: ContentStats | None = Field(None, description="统计信息")
    review: ReviewInfo | None = Field(None, description="审核信息")
    meta: ContentMeta | None = Field(None, description="元数据")
    extra: dict[str, Any] | None = Field(None, description="扩展字段")

    model_config = ConfigDict(from_attributes=True)


class ContentResponseOptions(BaseModel):
    """内容响应选项

    用于控制返回哪些可选字段
    """

    include_content: bool = Field(False, description="是否包含正文内容")
    include_stats: bool = Field(False, description="是否包含统计信息")
    include_review: bool = Field(False, description="是否包含审核信息")
    include_meta: bool = Field(False, description="是否包含元数据")
    include_category: bool = Field(True, description="是否包含分类信息")
    include_tags: bool = Field(True, description="是否包含标签")

    # 统计信息选项
    include_user_interactions: bool = Field(True, description="是否包含用户交互状态")

    # 权限相关
    current_user_id: int | None = Field(None, description="当前用户ID，用于计算用户相关状态")


class SimpleResponseBuilder:
    """简化的响应构建器

    兼容现有的ResponseFormatterMiddleware，直接返回数据，
    middleware会自动包装为 {"status": "success", "data": <返回值>}
    """

    @staticmethod
    def success(data: Any = None, message: str = None):
        """构建成功响应

        由于全局middleware的存在，直接返回数据即可
        如果需要包含消息，可以在数据中添加message字段
        """
        if message and isinstance(data, dict):
            data["message"] = message
            data["timestamp"] = datetime.utcnow().isoformat()
        return data

    @staticmethod
    def error(message: str, status_code: int = 400):
        """构建错误响应

        错误响应需要抛出HTTPException，让全局异常处理器处理
        """
        from fastapi import HTTPException

        raise HTTPException(status_code=status_code, detail=message)


# 向前引用解决
UnifiedContentResponse.model_rebuild()
