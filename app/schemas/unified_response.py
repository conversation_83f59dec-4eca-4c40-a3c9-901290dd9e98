"""
统一API响应模型设计

这个文件提供了一套统一的API响应模型，用于解决当前API返回值复杂、重复的问题。
"""

from datetime import datetime
from enum import Enum
from typing import Any, Generic, Optional, TypeVar

from pydantic import BaseModel, ConfigDict, Field

# 泛型类型变量
T = TypeVar("T")


class ResponseStatus(str, Enum):
    """响应状态枚举"""

    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"


class BaseResponse(BaseModel, Generic[T]):
    """基础响应模型

    注意: 由于项目使用了全局ResponseFormatterMiddleware，
    实际的API响应会被自动包装为 {"status": "success", "data": <this_model>}
    """

    success: bool | None = Field(None, description="操作是否成功")
    status: ResponseStatus | None = Field(None, description="响应状态")
    message: str | None = None
    data: T | None = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(from_attributes=True)


class ContentStats(BaseModel):
    """内容统计信息"""

    like_count: int = Field(0, description="点赞数")
    favorite_count: int = Field(0, description="收藏数")
    visit_count: int = Field(0, description="访问次数")
    comment_count: int = Field(0, description="评论数")
    share_count: int = Field(0, description="分享数")

    # 用户相关统计
    is_liked_by_user: bool = Field(False, description="当前用户是否已点赞")
    is_favorited_by_user: bool = Field(False, description="当前用户是否已收藏")
    is_followed_author: bool = Field(False, description="当前用户是否已关注作者")


class ContentMeta(BaseModel):
    """内容元数据"""

    content_type: str = Field(..., description="内容类型: article, video")
    content_id: int = Field(..., description="内容ID")
    slug: str | None = Field(None, description="内容别名")
    seo_title: str | None = Field(None, description="SEO标题")
    seo_description: str | None = Field(None, description="SEO描述")
    keywords: list[str] | None = Field(None, description="关键词")


class UnifiedContentResponse(BaseModel):
    """统一内容响应模型

    这个模型可以用于文章、视频等所有内容类型的响应，
    通过可选字段来控制返回的数据内容。
    """

    # 基础字段
    id: int
    title: str
    description: str | None = None
    content: str | None = Field(None, description="内容正文，列表接口通常不返回")
    cover_url: str | None = None

    # 状态字段
    is_published: bool = False
    is_approved: bool = False

    # 时间字段
    created_at: datetime
    updated_at: datetime
    published_at: datetime | None = None

    # 关联字段
    author: "UserInfo"
    category: Optional["CategoryInfo"] = None
    tags: list[str] | None = None

    # 可选扩展字段
    stats: ContentStats | None = Field(None, description="统计信息")
    review: Optional["ReviewInfo"] = Field(None, description="审核信息")
    meta: ContentMeta | None = Field(None, description="元数据")
    extra: dict[str, Any] | None = Field(None, description="扩展字段")

    model_config = ConfigDict(from_attributes=True)


class UserInfo(BaseModel):
    """用户信息简化模型"""

    id: int
    username: str
    nickname: str | None = None
    avatar: str | None = None

    model_config = ConfigDict(from_attributes=True)


class CategoryInfo(BaseModel):
    """分类信息简化模型"""

    id: int
    name: str
    slug: str | None = None

    model_config = ConfigDict(from_attributes=True)


class ReviewInfo(BaseModel):
    """审核信息简化模型"""

    id: int
    status: str
    comment: str | None = None
    reviewer_id: int | None = None
    reviewed_at: datetime | None = None

    model_config = ConfigDict(from_attributes=True)


# 具体的响应类型别名
ArticleResponse = UnifiedContentResponse
VideoResponse = UnifiedContentResponse


class ResponseBuilder:
    """响应构建器

    用于构建不同类型的响应，简化API端点的代码。
    """

    @staticmethod
    def success(data: Any = None, message: str = None) -> BaseResponse:
        """构建成功响应"""
        return BaseResponse(success=True, status=ResponseStatus.SUCCESS, message=message, data=data)

    @staticmethod
    def error(message: str, data: Any = None) -> BaseResponse:
        """构建错误响应"""
        return BaseResponse(success=False, status=ResponseStatus.ERROR, message=message, data=data)


class ContentResponseOptions(BaseModel):
    """内容响应选项

    用于控制返回哪些可选字段
    """

    include_content: bool = Field(False, description="是否包含正文内容")
    include_stats: bool = Field(False, description="是否包含统计信息")
    include_review: bool = Field(False, description="是否包含审核信息")
    include_meta: bool = Field(False, description="是否包含元数据")
    include_category: bool = Field(True, description="是否包含分类信息")
    include_tags: bool = Field(True, description="是否包含标签")

    # 统计信息选项
    include_user_interactions: bool = Field(True, description="是否包含用户交互状态")

    # 权限相关
    current_user_id: int | None = Field(None, description="当前用户ID，用于计算用户相关状态")


# 向前引用解决
UnifiedContentResponse.model_rebuild()
