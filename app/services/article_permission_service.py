"""
文章权限服务类

专门处理文章权限逻辑的服务类，包含各种权限检查方法
"""

from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List

from app import models
from app.core.permission_system import (
    Action,
    Permission,
    PermissionChecker,
    ResourceType,
    Scope,
)


class ArticlePermissionService:
    """文章权限服务类"""

    @staticmethod
    async def check_article_access(
        db: AsyncSession, content: models.Article, current_user: Optional[models.User] = None
    ) -> bool:
        """
        检查文章访问权限

        Args:
            db: 数据库会话
            content: 文章对象
            current_user: 当前用户

        Returns:
            是否有权访问
        """
        if not content:
            return False

        # 检查对所有文章的读取权限
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.READ, Scope.ALL)
        ):
            return True

        # 检查对自己文章的读取权限
        if current_user and content.author_id == current_user.id:
            if await Permission<PERSON>hecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.READ, Scope.OWN)
            ):
                return True

        # 检查对公开文章的读取权限
        if content.is_published and content.is_approved:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.READ, Scope.PUBLIC), content
            ):
                return True

        return False

    @staticmethod
    async def check_article_update_permission(
        db: AsyncSession, content: models.Article, current_user: models.User
    ) -> bool:
        """
        检查文章更新权限
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.UPDATE, Scope.ALL)
        ):
            return True

        if current_user and content.author_id == current_user.id:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.UPDATE, Scope.OWN)
            ):
                return True

        return False

    @staticmethod
    async def check_article_delete_permission(
        db: AsyncSession, content: models.Article, current_user: models.User
    ) -> bool:
        """
        检查文章删除权限
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.DELETE, Scope.ALL)
        ):
            return True

        if current_user and content.author_id == current_user.id:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.DELETE, Scope.OWN)
            ):
                return True

        return False

    @staticmethod
    async def can_create_article(db: AsyncSession, current_user: Optional[models.User]) -> bool:
        """
        检查用户是否可以创建文章
        """
        if not current_user:
            return False

        return await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.CREATE, Scope.OWN)
        )

    @staticmethod
    async def can_publish_article(
        db: AsyncSession, current_user: models.User, article: models.Article
    ) -> bool:
        """
        检查用户是否可以发布文章
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.PUBLISH, Scope.ALL)
        ):
            return True

        if article.author_id == current_user.id:
            return await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.PUBLISH, Scope.OWN)
            )

        return False

    @staticmethod
    async def can_approve_article(db: AsyncSession, current_user: models.User) -> bool:
        """
        检查用户是否可以审核文章
        """
        return await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.APPROVE, Scope.ALL)
        )

    @staticmethod
    async def filter_accessible_articles(
        db: AsyncSession, user: Optional[models.User], article_ids: List[int]
    ) -> List[int]:
        """
        过滤用户可访问的文章ID列表
        
        Args:
            db: 数据库会话
            user: 当前用户
            article_ids: 文章ID列表
            
        Returns:
            用户可访问的文章ID列表
        """
        if not article_ids:
            return []
            
        from app import crud
        from sqlalchemy import and_, or_
        
        # 获取所有文章
        articles = await crud.article.get_multi_by_ids(db, ids=article_ids)
        
        accessible_ids = []
        for article in articles:
            # 检查每篇文章的访问权限
            if await ArticlePermissionService.check_article_access(db, article, user):
                accessible_ids.append(article.id)
                
        return accessible_ids


# 创建服务实例
article_permission_service = ArticlePermissionService()
