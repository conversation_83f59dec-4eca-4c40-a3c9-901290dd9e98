"""
统一响应服务

提供统一的响应构建逻辑，简化API端点代码并确保响应格式的一致性。
"""

import asyncio

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import models
from app.schemas.simple_unified_response import (
    CategoryInfo,
    ContentMeta,
    ContentResponseOptions,
    ContentStats,
    ReviewInfo,
    UnifiedContentResponse,
    UserInfo,
)


class UnifiedResponseService:
    """统一响应服务"""

    @staticmethod
    async def build_content_response(
        db: AsyncSession, content: models.Article | models.Video, options: ContentResponseOptions
    ) -> UnifiedContentResponse:
        """构建单个内容的响应"""

        # 基础响应数据
        response_data = {
            "id": content.id,
            "title": content.title,
            "description": content.description,
            "cover_url": getattr(content, "cover_url", None),
            "is_published": content.is_published,
            "is_approved": content.is_approved,
            "created_at": content.created_at,
            "updated_at": content.updated_at,
            "published_at": getattr(content, "published_at", None),
        }

        # 作者信息
        if hasattr(content, "author") and content.author:
            response_data["author"] = UserInfo(
                id=content.author.id,
                username=content.author.username,
                nickname=content.author.nickname,
                avatar=content.author.avatar,
            )

        # 可选字段处理
        if options.include_content:
            response_data["content"] = getattr(content, "content", None)

        if options.include_category and hasattr(content, "category") and content.category:
            response_data["category"] = CategoryInfo(
                id=content.category.id,
                name=content.category.name,
                slug=getattr(content.category, "slug", None),
            )

        if options.include_tags and hasattr(content, "tags") and content.tags:
            response_data["tags"] = [tag.name for tag in content.tags]

        # 统计信息
        if options.include_stats:
            response_data["stats"] = await UnifiedResponseService._build_content_stats(
                db, content, options.current_user_id, options.include_user_interactions
            )

        # 审核信息
        if options.include_review:
            response_data["review"] = await UnifiedResponseService._build_review_info(db, content)

        # 元数据
        if options.include_meta:
            response_data["meta"] = UnifiedResponseService._build_content_meta(content)

        return UnifiedContentResponse(**response_data)

    @staticmethod
    async def build_content_list_response(
        db: AsyncSession,
        contents: list[models.Article | models.Video],
        total: int,
        options: ContentResponseOptions,
        page: int | None = None,
        page_size: int | None = None,
    ):
        """构建内容列表响应

        注意：此方法已简化，直接返回items列表。
        如需分页信息，请使用CursorPaginationResponse。
        """
        from app.services.visit_cache_service import visit_cache_service

        # 批量获取访问次数
        content_items = [
            ("article" if isinstance(c, models.Article) else "video", c.id) for c in contents
        ]
        visit_counts = await visit_cache_service.batch_get_visit_count(content_items)

        # 构建每个内容项的响应
        tasks = []
        for content in contents:
            # 从预取的结果中获取访问次数
            content_key = (
                "article" if isinstance(content, models.Article) else "video",
                content.id,
            )
            preloaded_stats = {"visit_count": visit_counts.get(content_key, 0)}
            task = UnifiedResponseService.build_content_response(
                db, content, options, preloaded_stats
            )
            tasks.append(task)

        items = await asyncio.gather(*tasks)

        # 直接返回items列表，让ResponseFormatterMiddleware处理包装
        return items

    @staticmethod
    async def _build_content_stats(
        db: AsyncSession,
        content: models.Article | models.Video,
        current_user_id: int | None,
        include_user_interactions: bool,
        preloaded_stats: dict | None = None,
    ) -> ContentStats:
        """构建内容统计信息（包含缓存逻辑）"""
        from app import crud
        from app.services.favorite_cache_service import favorite_cache_service
        from app.services.like_cache_service import like_cache_service
        from app.services.visit_cache_service import visit_cache_service

        if preloaded_stats is None:
            preloaded_stats = {}

        content_type = "article" if isinstance(content, models.Article) else "video"
        content_item = (content_type, content.id)

        # 1. 并行从缓存获取信息
        tasks = {
            "like": like_cache_service.batch_get_like_info([content_item], current_user_id),
            "favorite": favorite_cache_service.batch_get_favorite_info(
                [content_item], current_user_id
            ),
        }
        # 如果没有预加载访问次数，则从缓存获取
        if "visit_count" not in preloaded_stats:
            tasks["visit"] = visit_cache_service.get_visit_count(content_type, content.id)

        results = await asyncio.gather(*tasks.values())
        task_keys = list(tasks.keys())
        cached_results = dict(zip(task_keys, results, strict=False))

        cached_like_info = cached_results.get("like", {})
        cached_favorite_info = cached_results.get("favorite", {})
        visit_count = cached_results.get("visit", 0) or 0

        like_stats = cached_like_info.get(content_item, {})
        favorite_stats = cached_favorite_info.get(content_item, {})

        # 2. 检查缓存是否完整，确定是否需要查询数据库
        needs_db_query = (
            like_stats.get("like_count") is None
            or favorite_stats.get("favorite_count") is None
            or (
                include_user_interactions
                and current_user_id
                and (
                    like_stats.get("is_liked") is None or favorite_stats.get("is_favorited") is None
                )
            )
        )

        # 3. 如果缓存不完整，从数据库获取并回填缓存
        if needs_db_query:
            db_like_task = crud.like.get_content_likes_batch(
                db, content_items=[content_item], user_id=current_user_id
            )
            db_favorite_task = crud.favorite.get_content_favorites_batch(
                db, content_items=[content_item], user_id=current_user_id
            )
            db_like_result, db_favorite_result = await asyncio.gather(
                db_like_task, db_favorite_task
            )

            db_like_stats = db_like_result.get(content_item, {})
            db_favorite_stats = db_favorite_result.get(content_item, {})

            # 合并数据（数据库优先）并回填缓存
            like_stats = {**like_stats, **db_like_stats}
            favorite_stats = {**favorite_stats, **db_favorite_stats}

            # 并行回填缓存
            cache_tasks = [
                like_cache_service.set_like_count(
                    content_type, content.id, like_stats.get("like_count", 0)
                ),
                favorite_cache_service.set_favorite_count(
                    content_type, content.id, favorite_stats.get("favorite_count", 0)
                ),
            ]
            if current_user_id:
                cache_tasks.extend(
                    [
                        like_cache_service.set_user_like_status(
                            current_user_id,
                            content_type,
                            content.id,
                            like_stats.get("is_liked", False),
                        ),
                        favorite_cache_service.set_user_favorite_status(
                            current_user_id,
                            content_type,
                            content.id,
                            favorite_stats.get("is_favorited", False),
                        ),
                    ]
                )
            await asyncio.gather(*cache_tasks)

        # 4. 构建最终的统计信息
        stats_data = {
            "like_count": like_stats.get("like_count", 0),
            "favorite_count": favorite_stats.get("favorite_count", 0),
            "visit_count": preloaded_stats.get("visit_count", visit_count),
            "comment_count": 0,  # TODO: 实现评论数统计
            "share_count": 0,  # TODO: 实现分享数统计
        }

        if include_user_interactions and current_user_id:
            stats_data["is_liked_by_user"] = like_stats.get("is_liked", False)
            stats_data["is_favorited_by_user"] = favorite_stats.get("is_favorited", False)
            stats_data["is_followed_author"] = False  # TODO: 实现关注状态检查
        else:
            stats_data["is_liked_by_user"] = False
            stats_data["is_favorited_by_user"] = False
            stats_data["is_followed_author"] = False

        return ContentStats(**stats_data)

    @staticmethod
    async def _build_review_info(
        db: AsyncSession, content: models.Article | models.Video
    ) -> ReviewInfo | None:
        """构建审核信息"""
        from app import crud
        from app.models.review import ContentType

        # 获取内容类型
        content_type = (
            ContentType.ARTICLE if isinstance(content, models.Article) else ContentType.VIDEO
        )

        # 查询审核记录
        review = await crud.review.get_by_content(
            db, content_type=content_type, content_id=content.id
        )

        if not review:
            return None

        return ReviewInfo(
            id=review.id,
            status=review.status,
            comment=review.comment,
            reviewer_id=review.reviewer_id,
            reviewed_at=review.reviewed_at,
        )

    @staticmethod
    def _build_content_meta(content: models.Article | models.Video) -> ContentMeta:
        """构建内容元数据"""
        content_type = "article" if isinstance(content, models.Article) else "video"

        return ContentMeta(
            content_type=content_type,
            content_id=content.id,
            slug=getattr(content, "slug", None),
            seo_title=getattr(content, "seo_title", None),
            seo_description=getattr(content, "seo_description", None),
            keywords=getattr(content, "keywords", None),
        )


class ArticleResponseService(UnifiedResponseService):
    """文章响应服务"""

    @staticmethod
    async def get_article_detail(
        db: AsyncSession,
        article_id: int,
        current_user_id: int | None = None,
        include_stats: bool = False,
        include_review: bool = False,
    ) -> UnifiedContentResponse:
        """获取文章详情响应"""
        from app import crud

        # 获取文章
        article = await crud.article.get(
            db=db,
            id=article_id,
            options=[
                selectinload(models.Article.tags),
                selectinload(models.Article.author),
                selectinload(models.Article.category),
            ],
        )

        if not article:
            raise ValueError("文章不存在")

        # 构建响应选项
        options = ContentResponseOptions(
            include_content=True,  # 详情页包含正文
            include_stats=include_stats,
            include_review=include_review,
            current_user_id=current_user_id,
        )

        return await UnifiedResponseService.build_content_response(db, article, options)

    @staticmethod
    async def get_article_list(
        db: AsyncSession,
        articles: list[models.Article],
        total: int,
        current_user_id: int | None = None,
        include_stats: bool = False,
        include_review: bool = False,
        page: int | None = None,
        page_size: int | None = None,
    ):
        """获取文章列表响应"""

        # 构建响应选项
        options = ContentResponseOptions(
            include_content=False,  # 列表页不包含正文
            include_stats=include_stats,
            include_review=include_review,
            current_user_id=current_user_id,
        )

        return await UnifiedResponseService.build_content_list_response(
            db, articles, total, options, page, page_size
        )


# 创建全局实例
unified_response_service = UnifiedResponseService()
article_response_service = ArticleResponseService()
