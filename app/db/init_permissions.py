from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import Permission, User, UserRole


async def init_permissions(db: AsyncSession) -> None:
    """初始化权限数据"""
    # 检查是否已经完整初始化（检查角色权限分配）
    from sqlalchemy import func
    result = await db.execute(
        select(func.count()).select_from(
            select(UserRole.id).join(UserRole.permissions).where(UserRole.name == "普通用户")
        )
    )
    user_role_permissions_count = result.scalar()
    
    # 如果普通用户角色已有权限分配，说明已初始化
    if user_role_permissions_count > 0:
        return

    # 创建基本权限
    permissions = [
        # 用户资源权限
        Permission(
            name="查看所有用户",
            code="user:read:all",
            resource="user",
            action="read",
            description="允许查看所有用户",
        ),
        Permission(
            name="查看个人信息",
            code="user:read:own",
            resource="user",
            action="read",
            description="允许查看自己的信息",
        ),
        Permission(
            name="创建用户",
            code="user:create:all",
            resource="user",
            action="create",
            description="允许创建新用户",
        ),
        Permission(
            name="更新个人信息",
            code="user:update:own",
            resource="user",
            action="update",
            description="允许更新自己的信息",
        ),
        Permission(
            name="更新所有用户",
            code="user:update:all",
            resource="user",
            action="update",
            description="允许更新所有用户信息",
        ),
        Permission(
            name="删除用户",
            code="user:delete:all",
            resource="user",
            action="delete",
            description="允许删除用户",
        ),
        Permission(
            name="关注用户",
            code="user:follow:all",
            resource="user",
            action="follow",
            description="允许关注其他用户",
        ),
        Permission(
            name="管理用户",
            code="user:manage:all",
            resource="user",
            action="manage",
            description="允许管理用户",
        ),
        # 角色资源权限
        Permission(
            name="查看所有角色",
            code="role:read:all",
            resource="role",
            action="read",
            description="允许查看所有角色",
        ),
        Permission(
            name="创建角色",
            code="role:create:all",
            resource="role",
            action="create",
            description="允许创建新角色",
        ),
        Permission(
            name="更新角色",
            code="role:update:all",
            resource="role",
            action="update",
            description="允许更新角色信息",
        ),
        Permission(
            name="删除角色",
            code="role:delete:all",
            resource="role",
            action="delete",
            description="允许删除角色",
        ),
        Permission(
            name="管理角色",
            code="role:manage:all",
            resource="role",
            action="manage",
            description="允许管理角色",
        ),
        # 权限资源权限
        Permission(
            name="查看所有权限",
            code="permission:read:all",
            resource="permission",
            action="read",
            description="允许查看所有权限",
        ),
        Permission(
            name="管理权限",
            code="permission:manage:all",
            resource="permission",
            action="manage",
            description="允许管理权限",
        ),
        # 文章资源权限
        Permission(
            name="查看公开文章",
            code="article:read:public",
            resource="article",
            action="read",
            description="允许查看已发布且已审核的文章",
        ),
        Permission(
            name="查看所有文章",
            code="article:read:all",
            resource="article",
            action="read",
            description="允许查看所有文章",
        ),
        Permission(
            name="查看个人文章",
            code="article:read:own",
            resource="article",
            action="read",
            description="允许查看自己创建的文章",
        ),
        Permission(
            name="创建文章",
            code="article:create:own",
            resource="article",
            action="create",
            description="允许创建新文章",
        ),
        Permission(
            name="更新个人文章",
            code="article:update:own",
            resource="article",
            action="update",
            description="允许更新自己创建的文章",
        ),
        Permission(
            name="更新所有文章",
            code="article:update:all",
            resource="article",
            action="update",
            description="允许更新任何文章",
        ),
        Permission(
            name="发布个人文章",
            code="article:publish:own",
            resource="article",
            action="publish",
            description="允许发布自己创建的文章",
        ),
        Permission(
            name="发布所有文章",
            code="article:publish:all",
            resource="article",
            action="publish",
            description="允许发布任何文章",
        ),
        Permission(
            name="审核文章",
            code="article:approve:all",
            resource="article",
            action="approve",
            description="允许审核文章",
        ),
        Permission(
            name="删除个人文章",
            code="article:delete:own",
            resource="article",
            action="delete",
            description="允许删除自己创建的文章",
        ),
        Permission(
            name="删除所有文章",
            code="article:delete:all",
            resource="article",
            action="delete",
            description="允许删除任何文章",
        ),
        Permission(
            name="管理文章",
            code="article:manage:all",
            resource="article",
            action="manage",
            description="允许管理文章系统",
        ),
    ]

    db.add_all(permissions)
    await db.commit()

    # 创建基本角色
    admin_role = UserRole(
        name="管理员",
        desc="系统管理员，拥有所有权限",
        is_active=True,
    )

    user_role = UserRole(
        name="普通用户",
        desc="普通用户，拥有基本查看权限",
        is_default=True,
        is_active=True,
    )

    guest_role = UserRole(
        name="访客",
        desc="访客用户，只有有限的查看权限",
        is_active=True,
    )

    db.add_all([admin_role, user_role, guest_role])
    await db.commit()

    # 分配权限给角色
    # 管理员拥有所有权限
    admin_role.permissions = permissions

    # 为普通用户分配权限
    user_permission_codes = {
        "user:read:own",
        "user:update:own",
        "user:follow:all",
        "article:read:public",
        "article:read:own",
        "article:create:own",
        "article:update:own",
        "article:publish:own",
        "article:delete:own",
    }
    user_role.permissions = [p for p in permissions if p.code in user_permission_codes]

    # 为访客分配权限
    guest_permission_codes = {
        "article:read:public",
        "user:read:all",  # 允许查看用户公开信息
    }
    guest_role.permissions = [p for p in permissions if p.code in guest_permission_codes]

    await db.commit()

    # 创建超级管理员用户（如果不存在）
    result = await db.execute(select(User).where(User.username == "admin").limit(1))
    if not result.scalar_one_or_none():
        admin_user = User(
            username="admin",
            password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            nickname="超级管理员",
            email="<EMAIL>",
            role_id=admin_role.id,
            is_active=True,
            is_superuser=True,
        )
        db.add(admin_user)
        await db.commit()
