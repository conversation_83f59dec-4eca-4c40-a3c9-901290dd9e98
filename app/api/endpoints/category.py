from datetime import datetime, timed<PERSON>ta
from typing import Literal

from fastapi import APIRouter, Depends, Query, status
from fastapi.exceptions import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.schemas.simple_unified_response import ContentResponseOptions
from app.schemas.unified_response import (
    UnifiedContentResponse,
)
from app.services.unified_response_service import UnifiedResponseService

router = APIRouter()


@router.get(
    "/tree",
    response_model=list[schemas.Category],
    summary="获取分类层级树",
)
async def get_category_tree(
    db: AsyncSession = Depends(deps.get_db),
    category_type: Literal["article", "video"] | None = Query(None, description="分类类型"),
) -> list[schemas.Category]:
    """
    获取所有分类的层级结构树。
    这对于构建分类导航菜单非常有用。
    """
    return await crud.category.get_category_tree(db, category_type=category_type)


@router.get(
    "/articles",
    response_model=CursorPaginationResponse[UnifiedContentResponse],
    summary="获取文章列表（支持按分类）",
)
async def get_articles_by_category(
    *,
    db: AsyncSession = Depends(deps.get_db),
    category_id: int | None = Query(None, description="分类ID (可选,不提供则获取所有分类的文章)"),
    include_subcategories: bool = Query(True, description="是否包含子分类的文章"),
    cursor: str | None = Query(None, description="游标位置"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    sort_by: str = Query(
        "default", description="排序模式: default (默认), hot (最热), newest (最新)"
    ),
    order_by: str = Query("id", description="排序字段 (在 sort_by='default' 时有效)"),
    order_direction: str = Query(
        "desc", pattern="^(asc|desc)$", description="排序方向 (在 sort_by='default' 时有效)"
    ),
    time_range: str = Query(
        "week", description="时间范围 (在 sort_by='hot' 时有效): week, month, all"
    ),
    include_stats: bool = Query(False, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_total: bool = Query(False, description="是否包含总数量"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> CursorPaginationResponse[UnifiedContentResponse]:
    """
    获取指定分类下的文章列表。
    - 支持通过 `include_subcategories` 参数控制是否包含所有子孙分类的文章。
    - 支持多种排序模式: `default`, `hot`, `newest`。
    - 如果 `category_id` 未提供，则获取所有分类的文章。
    """
    category_ids = []
    if category_id:
        # 检查分类是否存在
        category = await crud.category.get(db, id=category_id)
        if not category:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="分类不存在")

        category_ids = [category_id]
        if include_subcategories:
            descendant_ids = await crud.category.get_descendant_ids(db, category_id=category_id)
            category_ids.extend(descendant_ids)

    options = ContentResponseOptions(
        include_content=False,
        include_stats=include_stats,
        include_review=include_review,
        current_user_id=current_user.id if current_user else None,
    )

    paginated_result = None

    if sort_by == "hot":
        time_deltas = {"week": timedelta(days=7), "month": timedelta(days=30)}
        time_threshold = (
            datetime.utcnow() - time_deltas.get(time_range)
            if time_range in time_deltas
            else datetime.min
        )
        # 热度排序不支持游标，使用简单的skip/limit
        try:
            skip = int(cursor) if cursor else 0
        except (ValueError, TypeError):
            skip = 0

        hot_articles = await crud.article.get_hot_articles_by_category(
            db,
            category_ids=category_ids,
            time_threshold=time_threshold,
            skip=skip,
            limit=size,
        )
        # 对于热度榜，我们通常不计算总数和复杂的上一页/下一页逻辑
        paginated_result = CursorPaginationResponse(
            items=hot_articles,
            has_next=len(hot_articles) == size,
            has_previous=skip > 0,
            next_cursor=str(skip + size) if len(hot_articles) == size else None,
            previous_cursor=str(skip - size) if skip > 0 else None,
        )
    else:
        if sort_by == "newest":
            order_by = "created_at"
            order_direction = "desc"

        pagination_params = CursorPaginationParams(
            cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
        )

        filters = {"category_ids": category_ids, "status": "published"}

        paginated_result = await crud.article.get_paginated_articles(
            db=db,
            params=pagination_params,
            filters=filters,
            current_user=current_user,
            include_review=include_review,
            include_total=include_total,
        )

    items = [
        await UnifiedResponseService.build_content_response(db, article, options)
        for article in paginated_result.items
    ]

    response_data = CursorPaginationResponse(
        items=items,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )

    return response_data
