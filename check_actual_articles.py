#!/usr/bin/env python3
"""
检查实际数据库中的文章状态
验证用户报告的文章ID在数据库中的实际状态
"""

import asyncio
import sys
import os
from sqlalchemy import text

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.db.session import AsyncSessionLocal
except ImportError as e:
    print(f"导入错误: {e}")
    print("可能是Python版本兼容性问题，尝试简化检查...")
    sys.exit(1)

async def check_articles_in_db():
    """检查数据库中的文章状态"""
    print("=== 检查数据库中的实际文章状态 ===")
    
    # 用户报告的文章ID
    article_ids = [4, 9, 7, 15, 19, 21, 14, 3, 22, 1]
    
    async with AsyncSessionLocal() as db:
        try:
            # 查询这些文章的状态
            query = text("""
                SELECT id, title, is_published, is_approved, author_id, created_at
                FROM articles 
                WHERE id = ANY(:ids)
                ORDER BY id
            """)
            
            result = await db.execute(query, {"ids": article_ids})
            articles = result.fetchall()
            
            print(f"\n查询文章ID: {article_ids}")
            print(f"找到 {len(articles)} 篇文章:\n")
            
            public_count = 0
            private_count = 0
            
            print("ID\t标题\t\t\t发布\t审核\t作者ID\t创建时间")
            print("-" * 80)
            
            for article in articles:
                id, title, is_published, is_approved, author_id, created_at = article
                
                # 截断标题显示
                display_title = (title[:15] + '...') if len(title) > 15 else title
                
                status = "公开" if (is_published and is_approved) else "私有"
                if is_published and is_approved:
                    public_count += 1
                else:
                    private_count += 1
                
                print(f"{id}\t{display_title:<18}\t{is_published}\t{is_approved}\t{author_id}\t{created_at.strftime('%Y-%m-%d')}")
            
            print(f"\n统计:")
            print(f"公开文章 (已发布且已审核): {public_count} 篇")
            print(f"私有文章 (未发布或未审核): {private_count} 篇")
            
            # 找出缺失的文章ID
            found_ids = [article[0] for article in articles]
            missing_ids = set(article_ids) - set(found_ids)
            
            if missing_ids:
                print(f"\n⚠️  数据库中不存在的文章ID: {sorted(missing_ids)}")
            else:
                print(f"\n✅ 所有请求的文章ID都存在于数据库中")
            
            # 预期的可访问文章（公开文章）
            expected_accessible = [article[0] for article in articles if article[2] and article[3]]
            print(f"\n预期未登录用户可访问的文章ID: {sorted(expected_accessible)}")
            
            return expected_accessible
            
        except Exception as e:
            print(f"查询数据库时出错: {e}")
            import traceback
            traceback.print_exc()
            return []

async def test_actual_permission_service():
    """测试实际的权限服务"""
    print("\n=== 测试实际权限服务 ===")
    
    try:
        from app.services.article_permission_service import article_permission_service
        
        article_ids = [4, 9, 7, 15, 19, 21, 14, 3, 22, 1]
        
        async with AsyncSessionLocal() as db:
            # 测试未登录用户的权限
            accessible_articles = await article_permission_service.filter_accessible_articles(
                db=db, user=None, article_ids=article_ids
            )
            
            print(f"实际权限服务返回的可访问文章ID: {accessible_articles}")
            
            if accessible_articles:
                print(f"✅ 权限服务修复成功！返回了 {len(accessible_articles)} 篇可访问文章")
            else:
                print(f"❌ 权限服务仍有问题，返回空列表")
            
            return accessible_articles
            
    except Exception as e:
        print(f"测试权限服务时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

async def main():
    """主函数"""
    print("检查实际数据库文章状态")
    print("=" * 50)
    
    # 检查数据库中的文章
    expected_accessible = await check_articles_in_db()
    
    # 测试实际权限服务
    actual_accessible = await test_actual_permission_service()
    
    print("\n" + "=" * 50)
    print("对比结果:")
    print(f"数据库中的公开文章: {sorted(expected_accessible)}")
    print(f"权限服务返回结果: {sorted(actual_accessible)}")
    
    if set(expected_accessible) == set(actual_accessible):
        print("\n🎉 完美匹配！权限服务正确返回了所有公开文章")
    elif actual_accessible:
        print("\n✅ 权限服务有返回结果，但可能不完全匹配")
        missing = set(expected_accessible) - set(actual_accessible)
        extra = set(actual_accessible) - set(expected_accessible)
        if missing:
            print(f"   缺失的公开文章: {sorted(missing)}")
        if extra:
            print(f"   多返回的文章: {sorted(extra)}")
    else:
        print("\n❌ 权限服务仍返回空列表，修复可能未完全生效")

if __name__ == "__main__":
    asyncio.run(main())